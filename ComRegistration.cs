using Microsoft.Win32;
using System.Runtime.InteropServices;
using System.Reflection;

namespace OpcDaServer
{
    /// <summary>
    /// COM 注册管理器
    /// </summary>
    public static class ComRegistration
    {
        private const string OPC_SERVER_CLSID = "{12345678-1234-1234-1234-123456789ABC}";
        private const string OPC_SERVER_PROGID = "OpcDaServer.Server";
        private const string OPC_SERVER_DESCRIPTION = "Simple OPC DA Server";
        private const string OPC_CATEGORY_CLSID = "{63D5F432-CFE4-11D1-B2C8-0060083BA1FB}"; // OPC DA Servers 2.0

        /// <summary>
        /// 注册 COM 服务器
        /// </summary>
        public static bool RegisterServer()
        {
            try
            {
                Console.WriteLine("正在注册 OPC DA Server...");

                // 获取当前执行的程序路径
                string assemblyPath = Assembly.GetExecutingAssembly().Location;
                string assemblyDir;

                // 处理单文件应用程序的情况
                if (string.IsNullOrEmpty(assemblyPath))
                {
                    assemblyDir = AppContext.BaseDirectory;
                    assemblyPath = Path.Combine(assemblyDir, "OpcDaServer.exe");
                }
                else
                {
                    assemblyDir = Path.GetDirectoryName(assemblyPath) ?? "";
                }

                // 构建 exe 文件路径
                string exePath;
                if (assemblyPath.EndsWith(".dll"))
                {
                    // 如果是 dll，构建对应的 exe 路径
                    string fileName = Path.GetFileNameWithoutExtension(assemblyPath);
                    exePath = Path.Combine(assemblyDir, $"{fileName}.exe");
                }
                else
                {
                    // 如果已经是 exe
                    exePath = assemblyPath;
                }

                Console.WriteLine($"程序路径: {exePath}");

                // 注册 CLSID
                RegisterCLSID(exePath);

                // 注册 ProgID
                RegisterProgID();

                // 注册到 OPC 类别
                RegisterOPCCategory();

                Console.WriteLine("OPC DA Server 注册成功!");
                Console.WriteLine($"CLSID: {OPC_SERVER_CLSID}");
                Console.WriteLine($"ProgID: {OPC_SERVER_PROGID}");
                
                return true;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"注册失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 注销 COM 服务器
        /// </summary>
        public static bool UnregisterServer()
        {
            try
            {
                Console.WriteLine("正在注销 OPC DA Server...");

                // 从 OPC 类别中移除
                UnregisterOPCCategory();

                // 删除 ProgID 注册
                UnregisterProgID();

                // 删除 CLSID 注册
                UnregisterCLSID();

                Console.WriteLine("OPC DA Server 注销成功!");
                return true;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"注销失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 注册 CLSID
        /// </summary>
        private static void RegisterCLSID(string assemblyPath)
        {
            // 获取 exe 文件路径
            string exePath = assemblyPath.Replace(".dll", ".exe");
            if (!File.Exists(exePath))
            {
                // 如果是 dll 路径，尝试构建 exe 路径
                string directory = Path.GetDirectoryName(assemblyPath) ?? "";
                string fileName = Path.GetFileNameWithoutExtension(assemblyPath);
                exePath = Path.Combine(directory, $"{fileName}.exe");
            }

            using var clsidKey = Registry.ClassesRoot.CreateSubKey($"CLSID\\{OPC_SERVER_CLSID}");
            clsidKey.SetValue("", OPC_SERVER_DESCRIPTION);
            clsidKey.SetValue("AppID", OPC_SERVER_CLSID);

            // 对于 OPC DA Server，主要使用 LocalServer32 注册
            using var localKey = clsidKey.CreateSubKey("LocalServer32");
            localKey.SetValue("", $"\"{exePath}\"");

            // 注册 ProgID
            using var progIdKey = clsidKey.CreateSubKey("ProgID");
            progIdKey.SetValue("", OPC_SERVER_PROGID);

            // 注册 Programmable
            clsidKey.CreateSubKey("Programmable");

            Console.WriteLine($"注册 EXE 路径: {exePath}");
        }

        /// <summary>
        /// 注册 ProgID
        /// </summary>
        private static void RegisterProgID()
        {
            using var progIdKey = Registry.ClassesRoot.CreateSubKey(OPC_SERVER_PROGID);
            progIdKey.SetValue("", OPC_SERVER_DESCRIPTION);

            using var clsidKey = progIdKey.CreateSubKey("CLSID");
            clsidKey.SetValue("", OPC_SERVER_CLSID);
        }

        /// <summary>
        /// 注册到 OPC 类别
        /// </summary>
        private static void RegisterOPCCategory()
        {
            try
            {
                // 注册到 OPC DA Servers 2.0 类别
                using var categoryKey = Registry.ClassesRoot.CreateSubKey($"CLSID\\{OPC_CATEGORY_CLSID}\\Implemented Categories\\{{63D5F432-CFE4-11D1-B2C8-0060083BA1FB}}");
                
                // 将服务器添加到类别中
                using var serverKey = Registry.ClassesRoot.CreateSubKey($"CLSID\\{OPC_SERVER_CLSID}\\Implemented Categories\\{{63D5F432-CFE4-11D1-B2C8-0060083BA1FB}}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"注册 OPC 类别时出现警告: {ex.Message}");
            }
        }

        /// <summary>
        /// 注销 CLSID
        /// </summary>
        private static void UnregisterCLSID()
        {
            try
            {
                Registry.ClassesRoot.DeleteSubKeyTree($"CLSID\\{OPC_SERVER_CLSID}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"删除 CLSID 注册时出现警告: {ex.Message}");
            }
        }

        /// <summary>
        /// 注销 ProgID
        /// </summary>
        private static void UnregisterProgID()
        {
            try
            {
                Registry.ClassesRoot.DeleteSubKeyTree(OPC_SERVER_PROGID);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"删除 ProgID 注册时出现警告: {ex.Message}");
            }
        }

        /// <summary>
        /// 从 OPC 类别中移除
        /// </summary>
        private static void UnregisterOPCCategory()
        {
            try
            {
                using var serverKey = Registry.ClassesRoot.OpenSubKey($"CLSID\\{OPC_SERVER_CLSID}\\Implemented Categories", true);
                serverKey?.DeleteSubKeyTree("{63D5F432-CFE4-11D1-B2C8-0060083BA1FB}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"从 OPC 类别移除时出现警告: {ex.Message}");
            }
        }

        /// <summary>
        /// 检查是否已注册
        /// </summary>
        public static bool IsRegistered()
        {
            try
            {
                using var clsidKey = Registry.ClassesRoot.OpenSubKey($"CLSID\\{OPC_SERVER_CLSID}");
                using var progIdKey = Registry.ClassesRoot.OpenSubKey(OPC_SERVER_PROGID);
                
                return clsidKey != null && progIdKey != null;
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// 显示注册状态
        /// </summary>
        public static void ShowRegistrationStatus()
        {
            bool isRegistered = IsRegistered();
            Console.WriteLine($"OPC DA Server 注册状态: {(isRegistered ? "已注册" : "未注册")}");
            
            if (isRegistered)
            {
                Console.WriteLine($"CLSID: {OPC_SERVER_CLSID}");
                Console.WriteLine($"ProgID: {OPC_SERVER_PROGID}");
            }
        }
    }
}
