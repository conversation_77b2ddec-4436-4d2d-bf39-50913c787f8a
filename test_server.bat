@echo off
echo ========================================
echo OPC DA Server 测试脚本
echo ========================================
echo.

echo 1. 检查当前注册状态
dotnet run -- /status
echo.

echo 2. 编译项目
dotnet build
if %errorlevel% neq 0 (
    echo 编译失败!
    pause
    exit /b 1
)
echo 编译成功!
echo.

echo 3. 显示帮助信息
dotnet run -- /help
echo.

echo ========================================
echo 测试完成!
echo ========================================
echo.
echo 要启动服务器，请运行: dotnet run
echo 要注册服务器，请以管理员身份运行: dotnet run -- /register
echo.
pause
