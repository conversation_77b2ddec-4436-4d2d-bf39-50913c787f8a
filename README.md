# OPC DA Server - .NET 8 实现

这是一个基于 .NET 8 的简单 OPC DA Server 实现，支持基本的数据读取功能。

## 功能特性

- ✅ 基于 .NET 8 框架
- ✅ 实现标准 OPC DA COM 接口
- ✅ 支持 10 个模拟数据点
- ✅ 自动数据模拟和更新
- ✅ COM 组件注册/注销功能
- ✅ 控制台应用程序
- ✅ 基本日志输出

## 数据点说明

服务器提供 10 个模拟数据点：

| 数据点 | 数据类型 | 模拟模式 | 说明 |
|--------|----------|----------|------|
| Tag001-Tag004 | int | 递增模式 | 整数值，按不同步长递增 |
| Tag005-Tag007 | float | 正弦波模式 | 浮点值，不同频率的正弦波 |
| Tag008-Tag009 | bool | 随机模式 | 布尔值，随机切换 |
| Tag010 | string | 状态模式 | 字符串，状态循环切换 |

## 编译和运行

### 1. 编译项目

```bash
dotnet build
```

### 2. 注册 COM 组件

```bash
# 以管理员身份运行
dotnet run -- /register
```

### 3. 启动服务器

```bash
dotnet run
```

### 4. 注销 COM 组件

```bash
# 以管理员身份运行
dotnet run -- /unregister
```

## 命令行参数

- `/register` - 注册 OPC DA Server 到系统
- `/unregister` - 从系统注销 OPC DA Server  
- `/status` - 显示注册状态
- `/help` - 显示帮助信息

## 运行时命令

服务器运行时支持以下键盘命令：

- `q` - 退出服务器
- `s` - 显示服务器状态
- `d` - 显示数据项列表
- `h` - 显示帮助

## OPC 客户端连接

### 连接信息

- **CLSID**: `{12345678-1234-1234-1234-123456789ABC}`
- **ProgID**: `OpcDaServer.Server`
- **服务器名称**: Simple OPC DA Server

### 测试客户端

可以使用以下 OPC 客户端进行测试：

1. **OPC Expert** (Matrikon)
2. **Takebishi OPC Client**
3. **KEPServerEX OPC Client**
4. **任何标准的 OPC DA 客户端**

## 技术实现

### 核心接口

- `IOPCServer` - OPC 服务器主接口
- `IOPCItemMgt` - 数据项管理接口
- `IOPCDataCallback` - 数据变化回调接口

### 主要组件

- `OpcServer` - OPC 服务器主实现
- `OpcGroup` - OPC 组管理
- `OpcItem` - 数据项定义
- `DataSimulator` - 数据模拟器
- `ComRegistration` - COM 注册管理

## 系统要求

- Windows 操作系统
- .NET 8 Runtime
- 管理员权限（用于 COM 注册）

## 限制说明

本实现仅支持以下功能：

- ✅ 数据读取操作
- ❌ 数据写入操作
- ❌ 数据持久化存储
- ❌ 复杂的配置管理
- ❌ 报警和事件功能
- ❌ Web 界面

## 故障排除

### 常见问题

1. **COM 注册失败**
   - 确保以管理员身份运行
   - 检查 .NET 8 Runtime 是否正确安装

2. **客户端无法连接**
   - 确认服务器已注册：`dotnet run -- /status`
   - 检查防火墙设置
   - 确认服务器正在运行

3. **数据读取失败**
   - 检查数据点名称是否正确（Tag001-Tag010）
   - 确认组和数据项已正确添加

## 开发说明

### 扩展数据点

要添加更多数据点，修改 `DataSimulator.cs` 中的 `InitializeSimulationData()` 方法。

### 修改数据类型

在 `OpcItem.cs` 中的 `GetComDataType()` 方法中添加新的数据类型支持。

### 自定义模拟模式

在 `DataSimulator.cs` 中添加新的 `SimulationType` 和相应的生成逻辑。

## 许可证

本项目仅用于学习和测试目的。
