using System.Runtime.InteropServices;

namespace OpcDaServer
{
    /// <summary>
    /// OPC DA 服务器主实现类
    /// </summary>
    [ComVisible(true)]
    [Guid("12345678-1234-1234-1234-123456789ABC")]
    [ClassInterface(ClassInterfaceType.None)]
    [ProgId("OpcDaServer.Server")]
    public class OpcServer : IOPCServer
    {
        private readonly OpcItemManager _itemManager;
        private readonly Dictionary<uint, OpcGroup> _groups = new();
        private readonly Dictionary<string, uint> _groupNameToHandle = new();
        private uint _nextGroupHandle = 1;
        private readonly DateTime _startTime;
        private OPCSERVERSTATE _serverState = OPCSERVERSTATE.OPC_STATUS_RUNNING;

        public OpcServer()
        {
            _itemManager = new OpcItemManager();
            _startTime = DateTime.Now;
            Console.WriteLine("OPC DA Server 已创建");
        }

        public OpcItemManager ItemManager => _itemManager;

        public int AddGroup(string szName, int bActive, uint dwRequestedUpdateRate, uint hClientGroup, 
            IntPtr pTimeBias, IntPtr pPercentDeadband, uint dwLCID, 
            out uint phServerGroup, out uint pRevisedUpdateRate, out IOPCItemMgt ppUnk)
        {
            Console.WriteLine($"AddGroup called: Name={szName}, Active={bActive != 0}, UpdateRate={dwRequestedUpdateRate}");

            try
            {
                // 检查组名是否已存在
                if (!string.IsNullOrEmpty(szName) && _groupNameToHandle.ContainsKey(szName))
                {
                    phServerGroup = 0;
                    pRevisedUpdateRate = 0;
                    ppUnk = null!;
                    return unchecked((int)0x80040202); // OPC_E_DUPLICATENAME
                }

                // 创建新组
                uint serverHandle = _nextGroupHandle++;
                string groupName = string.IsNullOrEmpty(szName) ? $"Group{serverHandle}" : szName;
                
                // 修正更新率（最小100ms）
                uint revisedUpdateRate = Math.Max(dwRequestedUpdateRate, 100);

                var group = new OpcGroup(groupName, serverHandle, hClientGroup, revisedUpdateRate, _itemManager);
                group.IsActive = bActive != 0;

                _groups[serverHandle] = group;
                if (!string.IsNullOrEmpty(szName))
                {
                    _groupNameToHandle[szName] = serverHandle;
                }

                phServerGroup = serverHandle;
                pRevisedUpdateRate = revisedUpdateRate;
                ppUnk = group;

                Console.WriteLine($"Group '{groupName}' created with server handle {serverHandle}");
                return 0; // S_OK
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error in AddGroup: {ex.Message}");
                phServerGroup = 0;
                pRevisedUpdateRate = 0;
                ppUnk = null!;
                return unchecked((int)0x80004005); // E_FAIL
            }
        }

        public int GetErrorString(int dwError, uint dwLocale, out string ppString)
        {
            Console.WriteLine($"GetErrorString called: Error=0x{dwError:X8}");

            // 简单的错误字符串映射
            ppString = dwError switch
            {
                0 => "Success",
                unchecked((int)0x80040203) => "Unknown Item ID",
                unchecked((int)0x80040202) => "Duplicate Name",
                unchecked((int)0x80040004) => "Bad Type",
                unchecked((int)0x80004005) => "General Failure",
                unchecked((int)0x80004001) => "Not Implemented",
                _ => $"Unknown Error (0x{dwError:X8})"
            };

            return 0; // S_OK
        }

        public int GetGroupByName(string szName, out IOPCItemMgt ppUnk)
        {
            Console.WriteLine($"GetGroupByName called: Name={szName}");

            try
            {
                if (_groupNameToHandle.TryGetValue(szName, out uint handle))
                {
                    if (_groups.TryGetValue(handle, out var group))
                    {
                        ppUnk = group;
                        return 0; // S_OK
                    }
                }

                ppUnk = null!;
                return unchecked((int)0x80040203); // OPC_E_UNKNOWNITEMID
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error in GetGroupByName: {ex.Message}");
                ppUnk = null!;
                return unchecked((int)0x80004005); // E_FAIL
            }
        }

        public int GetStatus(out OPCSERVERSTATUS ppServerStatus)
        {
            Console.WriteLine("GetStatus called");

            try
            {
                var currentTime = DateTime.Now;
                var lastUpdateTime = DateTime.Now; // 简化实现

                ppServerStatus = new OPCSERVERSTATUS
                {
                    ftStartTime = DateTimeToFileTime(_startTime),
                    ftCurrentTime = DateTimeToFileTime(currentTime),
                    ftLastUpdateTime = DateTimeToFileTime(lastUpdateTime),
                    dwServerState = _serverState,
                    dwGroupCount = (uint)_groups.Count,
                    dwBandWidth = 0,
                    wMajorVersion = 1,
                    wMinorVersion = 0,
                    wBuildNumber = 1,
                    wReserved = 0,
                    szVendorInfo = "Simple OPC DA Server v1.0"
                };

                return 0; // S_OK
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error in GetStatus: {ex.Message}");
                ppServerStatus = new OPCSERVERSTATUS();
                return unchecked((int)0x80004005); // E_FAIL
            }
        }

        public int RemoveGroup(uint hServerGroup, int bForce)
        {
            Console.WriteLine($"RemoveGroup called: Handle={hServerGroup}, Force={bForce != 0}");

            try
            {
                if (_groups.TryGetValue(hServerGroup, out var group))
                {
                    _groups.Remove(hServerGroup);
                    if (!string.IsNullOrEmpty(group.Name))
                    {
                        _groupNameToHandle.Remove(group.Name);
                    }
                    Console.WriteLine($"Group '{group.Name}' removed");
                    return 0; // S_OK
                }

                return unchecked((int)0x80040203); // OPC_E_UNKNOWNITEMID
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error in RemoveGroup: {ex.Message}");
                return unchecked((int)0x80004005); // E_FAIL
            }
        }

        public int CreateGroupEnumerator(OPCENUMSCOPE dwScope, out IEnumUnknown ppUnk)
        {
            Console.WriteLine($"CreateGroupEnumerator called: Scope={dwScope}");
            ppUnk = null!;
            return unchecked((int)0x80004001); // E_NOTIMPL
        }

        /// <summary>
        /// 将 DateTime 转换为 FILETIME
        /// </summary>
        private static FILETIME DateTimeToFileTime(DateTime dateTime)
        {
            long fileTime = dateTime.ToFileTime();
            return new FILETIME
            {
                dwLowDateTime = (uint)(fileTime & 0xFFFFFFFF),
                dwHighDateTime = (uint)(fileTime >> 32)
            };
        }

        /// <summary>
        /// 设置服务器状态
        /// </summary>
        public void SetServerState(OPCSERVERSTATE state)
        {
            _serverState = state;
            Console.WriteLine($"Server state changed to: {state}");
        }

        /// <summary>
        /// 获取组数量
        /// </summary>
        public int GroupCount => _groups.Count;

        /// <summary>
        /// 获取所有组
        /// </summary>
        public IEnumerable<OpcGroup> GetAllGroups()
        {
            return _groups.Values;
        }
    }
}
