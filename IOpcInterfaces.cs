using System.Runtime.InteropServices;

namespace OpcDaServer
{
    // OPC DA 标准接口定义
    
    [ComImport]
    [Guid("39C13A4D-011E-11D0-9675-0020AFD8ADB3")]
    [InterfaceType(ComInterfaceType.InterfaceIsIUnknown)]
    public interface IOPCServer
    {
        [PreserveSig]
        int AddGroup(
            [MarshalAs(UnmanagedType.LPWStr)] string szName,
            int bActive,
            uint dwRequestedUpdateRate,
            uint hClientGroup,
            IntPtr pTimeBias,
            IntPtr pPercentDeadband,
            uint dwLCID,
            out uint phServerGroup,
            out uint pRevisedUpdateRate,
            [MarshalAs(UnmanagedType.Interface)] out IOPCItemMgt ppUnk);

        [PreserveSig]
        int GetErrorString(
            int dwError,
            uint dwLocale,
            [MarshalAs(UnmanagedType.LPWStr)] out string ppString);

        [PreserveSig]
        int GetGroupByName(
            [MarshalAs(UnmanagedType.LPWStr)] string szName,
            [MarshalAs(UnmanagedType.Interface)] out IOPCItemMgt ppUnk);

        [PreserveSig]
        int GetStatus(out OPCSERVERSTATUS ppServerStatus);

        [PreserveSig]
        int RemoveGroup(uint hServerGroup, int bForce);

        [PreserveSig]
        int CreateGroupEnumerator(
            OPCENUMSCOPE dwScope,
            [MarshalAs(UnmanagedType.Interface)] out IEnumUnknown ppUnk);
    }

    [ComImport]
    [Guid("39C13A54-011E-11D0-9675-0020AFD8ADB3")]
    [InterfaceType(ComInterfaceType.InterfaceIsIUnknown)]
    public interface IOPCItemMgt
    {
        [PreserveSig]
        int AddItems(
            uint dwCount,
            [MarshalAs(UnmanagedType.LPArray, SizeParamIndex = 0)] OPCITEMDEF[] pItemArray,
            [MarshalAs(UnmanagedType.LPArray, SizeParamIndex = 0)] out OPCITEMRESULT[] ppAddResults,
            [MarshalAs(UnmanagedType.LPArray, SizeParamIndex = 0)] out int[] ppErrors);

        [PreserveSig]
        int ValidateItems(
            uint dwCount,
            [MarshalAs(UnmanagedType.LPArray, SizeParamIndex = 0)] OPCITEMDEF[] pItemArray,
            int bBlobUpdate,
            [MarshalAs(UnmanagedType.LPArray, SizeParamIndex = 0)] out OPCITEMRESULT[] ppValidationResults,
            [MarshalAs(UnmanagedType.LPArray, SizeParamIndex = 0)] out int[] ppErrors);

        [PreserveSig]
        int RemoveItems(
            uint dwCount,
            [MarshalAs(UnmanagedType.LPArray, SizeParamIndex = 0)] uint[] phServer,
            [MarshalAs(UnmanagedType.LPArray, SizeParamIndex = 0)] out int[] ppErrors);

        [PreserveSig]
        int SetActiveState(
            uint dwCount,
            [MarshalAs(UnmanagedType.LPArray, SizeParamIndex = 0)] uint[] phServer,
            int bActive,
            [MarshalAs(UnmanagedType.LPArray, SizeParamIndex = 0)] out int[] ppErrors);

        [PreserveSig]
        int SetClientHandles(
            uint dwCount,
            [MarshalAs(UnmanagedType.LPArray, SizeParamIndex = 0)] uint[] phServer,
            [MarshalAs(UnmanagedType.LPArray, SizeParamIndex = 0)] uint[] phClient,
            [MarshalAs(UnmanagedType.LPArray, SizeParamIndex = 0)] out int[] ppErrors);

        [PreserveSig]
        int SetDatatypes(
            uint dwCount,
            [MarshalAs(UnmanagedType.LPArray, SizeParamIndex = 0)] uint[] phServer,
            [MarshalAs(UnmanagedType.LPArray, SizeParamIndex = 0)] short[] pRequestedDatatypes,
            [MarshalAs(UnmanagedType.LPArray, SizeParamIndex = 0)] out int[] ppErrors);

        [PreserveSig]
        int CreateEnumerator(
            [MarshalAs(UnmanagedType.Interface)] out IEnumOPCItemAttributes ppUnk);
    }

    [ComImport]
    [Guid("39C13A70-011E-11D0-9675-0020AFD8ADB3")]
    [InterfaceType(ComInterfaceType.InterfaceIsIUnknown)]
    public interface IOPCDataCallback
    {
        [PreserveSig]
        int OnDataChange(
            uint dwTransid,
            uint hGroup,
            int hrMasterquality,
            uint dwCount,
            [MarshalAs(UnmanagedType.LPArray, SizeParamIndex = 3)] uint[] phClientItems,
            [MarshalAs(UnmanagedType.LPArray, SizeParamIndex = 3)] object[] pvValues,
            [MarshalAs(UnmanagedType.LPArray, SizeParamIndex = 3)] short[] pwQualities,
            [MarshalAs(UnmanagedType.LPArray, SizeParamIndex = 3)] FILETIME[] pftTimeStamps,
            [MarshalAs(UnmanagedType.LPArray, SizeParamIndex = 3)] int[] pErrors);

        [PreserveSig]
        int OnReadComplete(
            uint dwTransid,
            uint hGroup,
            int hrMasterquality,
            uint dwCount,
            [MarshalAs(UnmanagedType.LPArray, SizeParamIndex = 3)] uint[] phClientItems,
            [MarshalAs(UnmanagedType.LPArray, SizeParamIndex = 3)] object[] pvValues,
            [MarshalAs(UnmanagedType.LPArray, SizeParamIndex = 3)] short[] pwQualities,
            [MarshalAs(UnmanagedType.LPArray, SizeParamIndex = 3)] FILETIME[] pftTimeStamps,
            [MarshalAs(UnmanagedType.LPArray, SizeParamIndex = 3)] int[] pErrors);

        [PreserveSig]
        int OnWriteComplete(
            uint dwTransid,
            uint hGroup,
            int hrMastererror,
            uint dwCount,
            [MarshalAs(UnmanagedType.LPArray, SizeParamIndex = 3)] uint[] pClienthandles,
            [MarshalAs(UnmanagedType.LPArray, SizeParamIndex = 3)] int[] pErrors);

        [PreserveSig]
        int OnCancelComplete(uint dwTransid, uint hGroup);
    }

    // OPC DA 结构体定义
    [StructLayout(LayoutKind.Sequential)]
    public struct OPCSERVERSTATUS
    {
        public FILETIME ftStartTime;
        public FILETIME ftCurrentTime;
        public FILETIME ftLastUpdateTime;
        public OPCSERVERSTATE dwServerState;
        public uint dwGroupCount;
        public uint dwBandWidth;
        public short wMajorVersion;
        public short wMinorVersion;
        public short wBuildNumber;
        public short wReserved;
        [MarshalAs(UnmanagedType.LPWStr)]
        public string szVendorInfo;
    }

    [StructLayout(LayoutKind.Sequential)]
    public struct OPCITEMDEF
    {
        [MarshalAs(UnmanagedType.LPWStr)]
        public string szAccessPath;
        [MarshalAs(UnmanagedType.LPWStr)]
        public string szItemID;
        public int bActive;
        public uint hClient;
        public uint dwBlobSize;
        public IntPtr pBlob;
        public short vtRequestedDataType;
        public short wReserved;
    }

    [StructLayout(LayoutKind.Sequential)]
    public struct OPCITEMRESULT
    {
        public uint hServer;
        public short vtCanonicalDataType;
        public short wReserved;
        public uint dwAccessRights;
        public uint dwBlobSize;
        public IntPtr pBlob;
    }

    [StructLayout(LayoutKind.Sequential)]
    public struct FILETIME
    {
        public uint dwLowDateTime;
        public uint dwHighDateTime;
    }

    // 枚举定义
    public enum OPCSERVERSTATE
    {
        OPC_STATUS_RUNNING = 1,
        OPC_STATUS_FAILED = 2,
        OPC_STATUS_NOCONFIG = 3,
        OPC_STATUS_SUSPENDED = 4,
        OPC_STATUS_TEST = 5
    }

    public enum OPCENUMSCOPE
    {
        OPC_ENUM_PRIVATE_CONNECTIONS = 1,
        OPC_ENUM_PUBLIC_CONNECTIONS = 2,
        OPC_ENUM_ALL_CONNECTIONS = 3,
        OPC_ENUM_PRIVATE = 4,
        OPC_ENUM_PUBLIC = 5,
        OPC_ENUM_ALL = 6
    }

    // 辅助接口
    [ComImport]
    [Guid("00000100-0000-0000-C000-000000000046")]
    [InterfaceType(ComInterfaceType.InterfaceIsIUnknown)]
    public interface IEnumUnknown
    {
        [PreserveSig]
        int Next(uint celt, [MarshalAs(UnmanagedType.LPArray, SizeParamIndex = 0)] IntPtr[] rgelt, out uint pceltFetched);
        [PreserveSig]
        int Skip(uint celt);
        [PreserveSig]
        int Reset();
        [PreserveSig]
        int Clone(out IEnumUnknown ppenum);
    }

    [ComImport]
    [Guid("39C13A55-011E-11D0-9675-0020AFD8ADB3")]
    [InterfaceType(ComInterfaceType.InterfaceIsIUnknown)]
    public interface IEnumOPCItemAttributes
    {
        [PreserveSig]
        int Next(uint celt, out IntPtr ppItemArray, out uint pceltFetched);
        [PreserveSig]
        int Skip(uint celt);
        [PreserveSig]
        int Reset();
        [PreserveSig]
        int Clone(out IEnumOPCItemAttributes ppenum);
    }
}
