using System.Runtime.InteropServices;

namespace OpcDaServer
{
    /// <summary>
    /// OPC 组实现 IOPCItemMgt 接口
    /// </summary>
    [ComVisible(true)]
    [Guid("39C13A54-011E-11D0-9675-0020AFD8ADB4")]
    [ClassInterface(ClassInterfaceType.None)]
    public class OpcGroup : IOPCItemMgt
    {
        private readonly OpcItemManager _itemManager;
        private readonly Dictionary<uint, uint> _serverToClientHandles = new();
        private readonly Dictionary<uint, uint> _clientToServerHandles = new();
        private uint _nextServerHandle = 1;

        public string Name { get; }
        public uint ServerHandle { get; }
        public uint ClientHandle { get; }
        public bool IsActive { get; set; }
        public uint UpdateRate { get; set; }

        public OpcGroup(string name, uint serverHandle, uint clientHandle, uint updateRate, OpcItemManager itemManager)
        {
            Name = name;
            ServerHandle = serverHandle;
            ClientHandle = clientHandle;
            UpdateRate = updateRate;
            IsActive = true;
            _itemManager = itemManager;
        }

        public int AddItems(uint dwCount, OPCITEMDEF[] pItemArray, out OPCITEMRESULT[] ppAddResults, out int[] ppErrors)
        {
            Console.WriteLine($"AddItems called with {dwCount} items");
            
            ppAddResults = new OPCITEMRESULT[dwCount];
            ppErrors = new int[dwCount];

            for (uint i = 0; i < dwCount; i++)
            {
                try
                {
                    var itemDef = pItemArray[i];
                    Console.WriteLine($"Adding item: {itemDef.szItemID}");

                    // 检查项目是否存在
                    if (!_itemManager.ItemExists(itemDef.szItemID))
                    {
                        ppErrors[i] = unchecked((int)0x80040203); // OPC_E_UNKNOWNITEMID
                        continue;
                    }

                    // 获取或创建服务器句柄
                    var item = _itemManager.GetItem(itemDef.szItemID);
                    if (item == null)
                    {
                        ppErrors[i] = unchecked((int)0x80040203); // OPC_E_UNKNOWNITEMID
                        continue;
                    }

                    uint serverHandle = item.ServerHandle;
                    
                    // 设置客户端句柄映射
                    _serverToClientHandles[serverHandle] = itemDef.hClient;
                    _clientToServerHandles[itemDef.hClient] = serverHandle;
                    item.ClientHandle = itemDef.hClient;
                    item.IsActive = itemDef.bActive != 0;

                    // 填充结果
                    ppAddResults[i] = new OPCITEMRESULT
                    {
                        hServer = serverHandle,
                        vtCanonicalDataType = item.GetComDataType(),
                        wReserved = 0,
                        dwAccessRights = 1, // OPC_READABLE
                        dwBlobSize = 0,
                        pBlob = IntPtr.Zero
                    };

                    ppErrors[i] = 0; // S_OK
                    Console.WriteLine($"Successfully added item {itemDef.szItemID} with server handle {serverHandle}");
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"Error adding item {i}: {ex.Message}");
                    ppErrors[i] = unchecked((int)0x80004005); // E_FAIL
                }
            }

            return 0; // S_OK
        }

        public int ValidateItems(uint dwCount, OPCITEMDEF[] pItemArray, int bBlobUpdate, out OPCITEMRESULT[] ppValidationResults, out int[] ppErrors)
        {
            Console.WriteLine($"ValidateItems called with {dwCount} items");
            
            ppValidationResults = new OPCITEMRESULT[dwCount];
            ppErrors = new int[dwCount];

            for (uint i = 0; i < dwCount; i++)
            {
                try
                {
                    var itemDef = pItemArray[i];
                    
                    if (_itemManager.ItemExists(itemDef.szItemID))
                    {
                        var item = _itemManager.GetItem(itemDef.szItemID);
                        if (item != null)
                        {
                            ppValidationResults[i] = new OPCITEMRESULT
                            {
                                hServer = 0, // 验证时不分配句柄
                                vtCanonicalDataType = item.GetComDataType(),
                                wReserved = 0,
                                dwAccessRights = 1, // OPC_READABLE
                                dwBlobSize = 0,
                                pBlob = IntPtr.Zero
                            };
                            ppErrors[i] = 0; // S_OK
                        }
                        else
                        {
                            ppErrors[i] = unchecked((int)0x80040203); // OPC_E_UNKNOWNITEMID
                        }
                    }
                    else
                    {
                        ppErrors[i] = unchecked((int)0x80040203); // OPC_E_UNKNOWNITEMID
                    }
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"Error validating item {i}: {ex.Message}");
                    ppErrors[i] = unchecked((int)0x80004005); // E_FAIL
                }
            }

            return 0; // S_OK
        }

        public int RemoveItems(uint dwCount, uint[] phServer, out int[] ppErrors)
        {
            Console.WriteLine($"RemoveItems called with {dwCount} items");
            
            ppErrors = new int[dwCount];

            for (uint i = 0; i < dwCount; i++)
            {
                try
                {
                    uint serverHandle = phServer[i];
                    
                    if (_serverToClientHandles.ContainsKey(serverHandle))
                    {
                        uint clientHandle = _serverToClientHandles[serverHandle];
                        _serverToClientHandles.Remove(serverHandle);
                        _clientToServerHandles.Remove(clientHandle);
                        ppErrors[i] = 0; // S_OK
                    }
                    else
                    {
                        ppErrors[i] = unchecked((int)0x80040203); // OPC_E_UNKNOWNITEMID
                    }
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"Error removing item {i}: {ex.Message}");
                    ppErrors[i] = unchecked((int)0x80004005); // E_FAIL
                }
            }

            return 0; // S_OK
        }

        public int SetActiveState(uint dwCount, uint[] phServer, int bActive, out int[] ppErrors)
        {
            Console.WriteLine($"SetActiveState called with {dwCount} items, active: {bActive != 0}");
            
            ppErrors = new int[dwCount];

            for (uint i = 0; i < dwCount; i++)
            {
                try
                {
                    uint serverHandle = phServer[i];
                    var item = _itemManager.GetItem(serverHandle);
                    
                    if (item != null)
                    {
                        item.IsActive = bActive != 0;
                        ppErrors[i] = 0; // S_OK
                    }
                    else
                    {
                        ppErrors[i] = unchecked((int)0x80040203); // OPC_E_UNKNOWNITEMID
                    }
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"Error setting active state for item {i}: {ex.Message}");
                    ppErrors[i] = unchecked((int)0x80004005); // E_FAIL
                }
            }

            return 0; // S_OK
        }

        public int SetClientHandles(uint dwCount, uint[] phServer, uint[] phClient, out int[] ppErrors)
        {
            Console.WriteLine($"SetClientHandles called with {dwCount} items");
            
            ppErrors = new int[dwCount];

            for (uint i = 0; i < dwCount; i++)
            {
                try
                {
                    uint serverHandle = phServer[i];
                    uint clientHandle = phClient[i];
                    
                    if (_serverToClientHandles.ContainsKey(serverHandle))
                    {
                        // 移除旧的映射
                        uint oldClientHandle = _serverToClientHandles[serverHandle];
                        _clientToServerHandles.Remove(oldClientHandle);
                        
                        // 设置新的映射
                        _serverToClientHandles[serverHandle] = clientHandle;
                        _clientToServerHandles[clientHandle] = serverHandle;
                        
                        var item = _itemManager.GetItem(serverHandle);
                        if (item != null)
                        {
                            item.ClientHandle = clientHandle;
                        }
                        
                        ppErrors[i] = 0; // S_OK
                    }
                    else
                    {
                        ppErrors[i] = unchecked((int)0x80040203); // OPC_E_UNKNOWNITEMID
                    }
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"Error setting client handle for item {i}: {ex.Message}");
                    ppErrors[i] = unchecked((int)0x80004005); // E_FAIL
                }
            }

            return 0; // S_OK
        }

        public int SetDatatypes(uint dwCount, uint[] phServer, short[] pRequestedDatatypes, out int[] ppErrors)
        {
            Console.WriteLine($"SetDatatypes called with {dwCount} items");
            
            ppErrors = new int[dwCount];

            for (uint i = 0; i < dwCount; i++)
            {
                // 简单实现：不支持数据类型转换
                ppErrors[i] = unchecked((int)0x80040004); // OPC_E_BADTYPE
            }

            return 0; // S_OK
        }

        public int CreateEnumerator(out IEnumOPCItemAttributes ppUnk)
        {
            Console.WriteLine("CreateEnumerator called");
            ppUnk = null!;
            return unchecked((int)0x80004001); // E_NOTIMPL
        }
    }
}
