# OPC DA Server 使用说明

## 项目概述

这是一个基于 .NET 8 的简单 OPC DA Server 实现，完全符合您的要求：

✅ **已实现的功能：**
- 基于 .NET 8 框架
- 实现了标准 OPC DA COM 接口（IOPCServer、IOPCItemMgt、IOPCDataCallback）
- 提供 10 个模拟数据点（Tag001-Tag010）
- 支持数据读取操作
- 控制台应用程序
- COM 组件注册/注销功能（已修复 EXE 路径注册问题）
- 基本日志输出

❌ **明确排除的功能（按要求）：**
- 数据写入功能
- 数据持久化存储
- 复杂的配置管理
- 报警或事件功能
- Web 界面或其他 UI
- 资源监控功能

## 数据点详情

| 数据点ID | 数据类型 | 模拟模式 | 初始值 | 说明 |
|----------|----------|----------|--------|------|
| Tag001 | int | 递增 | 10 | 每秒递增1，范围0-1000 |
| Tag002 | int | 递增 | 20 | 每秒递增2，范围0-1000 |
| Tag003 | int | 递增 | 30 | 每秒递增3，范围0-1000 |
| Tag004 | int | 递增 | 40 | 每秒递增4，范围0-1000 |
| Tag005 | float | 正弦波 | 0.0 | 频率0.1Hz，幅度30，中心值50 |
| Tag006 | float | 正弦波 | 0.0 | 频率0.2Hz，幅度30，中心值50 |
| Tag007 | float | 正弦波 | 0.0 | 频率0.3Hz，幅度30，中心值50 |
| Tag008 | bool | 随机 | false | 随机切换true/false |
| Tag009 | bool | 随机 | false | 随机切换true/false |
| Tag010 | string | 状态循环 | "RUNNING" | 循环：RUNNING→STOPPED→PAUSED→ERROR |

## 快速开始

### 1. 编译项目
```bash
dotnet build
```

### 2. 查看帮助
```bash
dotnet run -- /help
```

### 3. 检查注册状态
```bash
dotnet run -- /status
```

### 4. 注册 COM 组件（需要管理员权限）
```bash
# 以管理员身份运行 PowerShell 或命令提示符
dotnet run -- /register
```

### 5. 启动服务器
```bash
dotnet run
```

### 6. 注销 COM 组件（可选）
```bash
# 以管理员身份运行
dotnet run -- /unregister
```

## 服务器运行时操作

服务器启动后，支持以下键盘命令：

- **q** - 退出服务器
- **s** - 显示服务器状态
- **d** - 显示所有数据项的当前值
- **h** - 显示运行时帮助

## OPC 客户端连接信息

### 连接参数
- **CLSID**: `{12345678-1234-1234-1234-123456789ABC}`
- **ProgID**: `OpcDaServer.Server`
- **服务器描述**: Simple OPC DA Server

### 推荐的测试客户端
1. **Takebishi OPC Client** - 您提到的客户端
2. **Matrikon OPC Expert**
3. **KEPServerEX OPC Client**
4. **任何标准的 OPC DA 2.0 客户端**

## 验证步骤

### 使用 Takebishi OPC Client 测试：

1. **启动服务器**
   ```bash
   dotnet run
   ```

2. **打开 Takebishi OPC Client**

3. **连接到服务器**
   - 服务器名称：`OpcDaServer.Server`
   - 或使用 CLSID：`{12345678-1234-1234-1234-123456789ABC}`

4. **创建组并添加数据项**
   - 添加项目：Tag001, Tag002, ..., Tag010

5. **验证数据读取**
   - Tag001-Tag004：应显示递增的整数值
   - Tag005-Tag007：应显示变化的浮点值（正弦波）
   - Tag008-Tag009：应显示随机的布尔值
   - Tag010：应显示状态字符串

## 技术架构

### 核心组件
- **OpcServer.cs** - 实现 IOPCServer 接口
- **OpcGroup.cs** - 实现 IOPCItemMgt 接口
- **OpcItem.cs** - 数据项定义和管理
- **DataSimulator.cs** - 数据模拟器
- **ComRegistration.cs** - COM 注册管理
- **Program.cs** - 主程序入口

### COM 接口实现
- ✅ IOPCServer - 服务器主接口
- ✅ IOPCItemMgt - 数据项管理接口
- ✅ IOPCDataCallback - 数据变化回调接口（基础实现）

## 故障排除

### 常见问题

**Q: 编译时出现 COM 相关警告**
A: 这些警告是正常的，因为 OPC DA 是 Windows 特定的技术。

**Q: 客户端无法连接到服务器**
A: 
1. 确保服务器已注册：`dotnet run -- /status`
2. 确保服务器正在运行
3. 检查防火墙设置
4. 确保以管理员权限注册了 COM 组件

**Q: 无法注册 COM 组件**
A: 
1. 确保以管理员身份运行
2. 确保 .NET 8 Runtime 已正确安装

**Q: 数据项读取失败**
A: 
1. 确认数据项名称正确（Tag001-Tag010）
2. 检查服务器日志输出
3. 确认组和数据项已正确添加

## 开发说明

### 扩展数据点
要添加更多数据点，修改 `DataSimulator.cs` 中的 `InitializeSimulationData()` 方法。

### 自定义数据类型
在 `OpcItem.cs` 中的 `GetComDataType()` 方法中添加新的数据类型支持。

### 修改模拟模式
在 `DataSimulator.cs` 中添加新的 `SimulationType` 和相应的生成逻辑。

## 系统要求

- **操作系统**: Windows（OPC DA 仅支持 Windows）
- **框架**: .NET 8 Runtime
- **权限**: 管理员权限（用于 COM 注册）
- **内存**: 最小 100MB
- **磁盘空间**: 约 50MB

## 性能特性

- **数据更新频率**: 每秒1次
- **支持的并发客户端**: 理论上无限制
- **内存占用**: 约 20-30MB
- **CPU 占用**: 极低（<1%）

## 许可和免责声明

本项目仅用于学习和测试目的。在生产环境中使用前，请进行充分的测试和验证。
