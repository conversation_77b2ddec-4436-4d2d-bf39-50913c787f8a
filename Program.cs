using System.Runtime.InteropServices;

namespace OpcDaServer
{
    /// <summary>
    /// OPC DA Server 主程序
    /// </summary>
    class Program
    {
        private static OpcServer? _opcServer;
        private static DataSimulator? _dataSimulator;
        private static bool _isRunning = false;

        static void Main(string[] args)
        {
            Console.WriteLine(System.Reflection.Assembly.GetExecutingAssembly().Location);
            return;
            Console.WriteLine("=== Simple OPC DA Server v1.0 ===");
            Console.WriteLine("基于 .NET 8 的 OPC DA 服务器");
            Console.WriteLine();

            // 处理命令行参数
            if (args.Length > 0)
            {
                switch (args[0].ToLower())
                {
                    case "/register":
                    case "-register":
                        RegisterServer();
                        return;

                    case "/unregister":
                    case "-unregister":
                        UnregisterServer();
                        return;

                    case "/status":
                    case "-status":
                        ShowStatus();
                        return;

                    case "/help":
                    case "-help":
                    case "/?":
                        ShowHelp();
                        return;
                }
            }

            // 启动服务器
            StartServer();
        }

        /// <summary>
        /// 启动 OPC 服务器
        /// </summary>
        private static void StartServer()
        {
            try
            {
                Console.WriteLine("正在启动 OPC DA Server...");

                // 检查注册状态
                if (!ComRegistration.IsRegistered())
                {
                    Console.WriteLine("警告: OPC Server 尚未注册到系统中");
                    Console.WriteLine("请使用 'OpcDaServer.exe /register' 命令进行注册");
                    Console.WriteLine();
                }

                // 创建 OPC 服务器实例
                _opcServer = new OpcServer();

                // 初始化数据模拟器
                _dataSimulator = new DataSimulator(_opcServer.ItemManager);
                _dataSimulator.Start();

                // 注册 COM 对象
                int cookie = 0;
                try
                {
                    // 注册类工厂
                    var guid = typeof(OpcServer).GUID;
                    var hr = Ole32.CoRegisterClassObject(
                        ref guid,
                        new ClassFactory<OpcServer>(),
                        CLSCTX.CLSCTX_LOCAL_SERVER,
                        REGCLS.REGCLS_MULTIPLEUSE,
                        out cookie);

                    if (hr != 0)
                    {
                        Console.WriteLine($"注册类工厂失败: HRESULT = 0x{hr:X8}");
                        return;
                    }

                    Console.WriteLine("OPC DA Server 启动成功!");
                    Console.WriteLine();
                    ShowServerInfo();
                    Console.WriteLine();
                    Console.WriteLine("按 'q' 退出服务器...");

                    _isRunning = true;

                    // 主循环
                    while (_isRunning)
                    {
                        var key = Console.ReadKey(true);
                        if (key.KeyChar == 'q' || key.KeyChar == 'Q')
                        {
                            _isRunning = false;
                        }
                        else if (key.KeyChar == 's' || key.KeyChar == 'S')
                        {
                            ShowServerStatus();
                        }
                        else if (key.KeyChar == 'd' || key.KeyChar == 'D')
                        {
                            ShowDataItems();
                        }
                        else if (key.KeyChar == 'h' || key.KeyChar == 'H')
                        {
                            ShowRuntimeHelp();
                        }

                        Thread.Sleep(100);
                    }
                }
                finally
                {
                    // 注销类工厂
                    if (cookie != 0)
                    {
                        Ole32.CoRevokeClassObject(cookie);
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"启动服务器时发生错误: {ex.Message}");
                Console.WriteLine($"详细信息: {ex}");
            }
            finally
            {
                // 清理资源
                _dataSimulator?.Stop();
                _dataSimulator?.Dispose();
                Console.WriteLine("OPC DA Server 已停止");
            }
        }

        /// <summary>
        /// 注册服务器
        /// </summary>
        private static void RegisterServer()
        {
            if (ComRegistration.RegisterServer())
            {
                Console.WriteLine("服务器注册成功!");
            }
            else
            {
                Console.WriteLine("服务器注册失败!");
                Environment.Exit(1);
            }
        }

        /// <summary>
        /// 注销服务器
        /// </summary>
        private static void UnregisterServer()
        {
            if (ComRegistration.UnregisterServer())
            {
                Console.WriteLine("服务器注销成功!");
            }
            else
            {
                Console.WriteLine("服务器注销失败!");
                Environment.Exit(1);
            }
        }

        /// <summary>
        /// 显示状态
        /// </summary>
        private static void ShowStatus()
        {
            ComRegistration.ShowRegistrationStatus();
        }

        /// <summary>
        /// 显示帮助信息
        /// </summary>
        private static void ShowHelp()
        {
            Console.WriteLine("用法: OpcDaServer.exe [选项]");
            Console.WriteLine();
            Console.WriteLine("选项:");
            Console.WriteLine("  /register    注册 OPC DA Server 到系统");
            Console.WriteLine("  /unregister  从系统注销 OPC DA Server");
            Console.WriteLine("  /status      显示注册状态");
            Console.WriteLine("  /help        显示此帮助信息");
            Console.WriteLine();
            Console.WriteLine("不带参数运行将启动 OPC DA Server");
        }

        /// <summary>
        /// 显示服务器信息
        /// </summary>
        private static void ShowServerInfo()
        {
            Console.WriteLine("=== 服务器信息 ===");
            Console.WriteLine($"CLSID: {{12345678-1234-1234-1234-123456789ABC}}");
            Console.WriteLine($"ProgID: OpcDaServer.Server");
            Console.WriteLine($"版本: 1.0.0.0");
            Console.WriteLine($"数据点数量: {_opcServer?.ItemManager.Count ?? 0}");
            Console.WriteLine($"组数量: {_opcServer?.GroupCount ?? 0}");
        }

        /// <summary>
        /// 显示服务器状态
        /// </summary>
        private static void ShowServerStatus()
        {
            Console.WriteLine();
            Console.WriteLine("=== 服务器状态 ===");
            Console.WriteLine($"运行状态: {(_isRunning ? "运行中" : "已停止")}");
            Console.WriteLine($"数据点数量: {_opcServer?.ItemManager.Count ?? 0}");
            Console.WriteLine($"组数量: {_opcServer?.GroupCount ?? 0}");
            Console.WriteLine($"当前时间: {DateTime.Now:yyyy-MM-dd HH:mm:ss}");
        }

        /// <summary>
        /// 显示数据项
        /// </summary>
        private static void ShowDataItems()
        {
            Console.WriteLine();
            Console.WriteLine("=== 数据项列表 ===");
            
            if (_opcServer?.ItemManager != null)
            {
                foreach (var item in _opcServer.ItemManager.GetAllItems())
                {
                    Console.WriteLine($"{item.ItemID}: {item.Value} ({item.DataType.Name}) - {item.Timestamp:HH:mm:ss}");
                }
            }
        }

        /// <summary>
        /// 显示运行时帮助
        /// </summary>
        private static void ShowRuntimeHelp()
        {
            Console.WriteLine();
            Console.WriteLine("=== 运行时命令 ===");
            Console.WriteLine("q - 退出服务器");
            Console.WriteLine("s - 显示服务器状态");
            Console.WriteLine("d - 显示数据项");
            Console.WriteLine("h - 显示此帮助");
            Console.WriteLine();
        }
    }

    /// <summary>
    /// COM 类工厂
    /// </summary>
    public class ClassFactory<T> : IClassFactory where T : new()
    {
        public int CreateInstance(IntPtr pUnkOuter, ref Guid riid, out IntPtr ppvObject)
        {
            ppvObject = IntPtr.Zero;

            if (pUnkOuter != IntPtr.Zero)
            {
                return unchecked((int)0x80040110); // CLASS_E_NOAGGREGATION
            }

            var instance = new T();
            ppvObject = Marshal.GetComInterfaceForObject(instance, typeof(T));
            return 0; // S_OK
        }

        public int LockServer(bool fLock)
        {
            return 0; // S_OK
        }
    }

    /// <summary>
    /// COM 接口定义
    /// </summary>
    [ComImport]
    [Guid("00000001-0000-0000-C000-000000000046")]
    [InterfaceType(ComInterfaceType.InterfaceIsIUnknown)]
    public interface IClassFactory
    {
        [PreserveSig]
        int CreateInstance(IntPtr pUnkOuter, ref Guid riid, out IntPtr ppvObject);
        
        [PreserveSig]
        int LockServer(bool fLock);
    }

    /// <summary>
    /// Ole32 API 声明
    /// </summary>
    public static class Ole32
    {
        [DllImport("ole32.dll")]
        public static extern int CoRegisterClassObject(
            [In] ref Guid rclsid,
            [In] IClassFactory pUnk,
            [In] CLSCTX dwClsContext,
            [In] REGCLS flags,
            out int lpdwRegister);

        [DllImport("ole32.dll")]
        public static extern int CoRevokeClassObject(int dwRegister);
    }

    public enum CLSCTX : uint
    {
        CLSCTX_INPROC_SERVER = 0x1,
        CLSCTX_INPROC_HANDLER = 0x2,
        CLSCTX_LOCAL_SERVER = 0x4,
        CLSCTX_REMOTE_SERVER = 0x10
    }

    public enum REGCLS : uint
    {
        REGCLS_SINGLEUSE = 0,
        REGCLS_MULTIPLEUSE = 1,
        REGCLS_MULTI_SEPARATE = 2,
        REGCLS_SUSPENDED = 4,
        REGCLS_SURROGATE = 8
    }
}
