using System.Runtime.InteropServices;

namespace OpcDaServer
{
    /// <summary>
    /// OPC 数据项类
    /// </summary>
    public class OpcItem
    {
        public string ItemID { get; set; } = string.Empty;
        public object Value { get; set; } = 0;
        public short Quality { get; set; } = 192; // OPC_QUALITY_GOOD
        public DateTime Timestamp { get; set; } = DateTime.Now;
        public Type DataType { get; set; } = typeof(int);
        public uint ServerHandle { get; set; }
        public uint ClientHandle { get; set; }
        public bool IsActive { get; set; } = true;

        public OpcItem(string itemId, Type dataType, object initialValue)
        {
            ItemID = itemId;
            DataType = dataType;
            Value = initialValue;
            Timestamp = DateTime.Now;
        }

        /// <summary>
        /// 更新数据项的值
        /// </summary>
        public void UpdateValue(object newValue)
        {
            Value = newValue;
            Timestamp = DateTime.Now;
        }

        /// <summary>
        /// 获取 COM 兼容的数据类型
        /// </summary>
        public short GetComDataType()
        {
            if (DataType == typeof(int))
                return 3; // VT_I4
            else if (DataType == typeof(float))
                return 4; // VT_R4
            else if (DataType == typeof(double))
                return 5; // VT_R8
            else if (DataType == typeof(bool))
                return 11; // VT_BOOL
            else if (DataType == typeof(string))
                return 8; // VT_BSTR
            else
                return 12; // VT_VARIANT
        }

        /// <summary>
        /// 将 DateTime 转换为 FILETIME
        /// </summary>
        public FILETIME GetFileTime()
        {
            long fileTime = Timestamp.ToFileTime();
            return new FILETIME
            {
                dwLowDateTime = (uint)(fileTime & 0xFFFFFFFF),
                dwHighDateTime = (uint)(fileTime >> 32)
            };
        }
    }

    /// <summary>
    /// OPC 数据项管理器
    /// </summary>
    public class OpcItemManager
    {
        private readonly Dictionary<uint, OpcItem> _items = new();
        private readonly Dictionary<string, uint> _itemIdToHandle = new();
        private uint _nextHandle = 1;

        /// <summary>
        /// 添加数据项
        /// </summary>
        public uint AddItem(string itemId, Type dataType, object initialValue)
        {
            if (_itemIdToHandle.ContainsKey(itemId))
            {
                return _itemIdToHandle[itemId];
            }

            uint handle = _nextHandle++;
            var item = new OpcItem(itemId, dataType, initialValue)
            {
                ServerHandle = handle
            };

            _items[handle] = item;
            _itemIdToHandle[itemId] = handle;

            return handle;
        }

        /// <summary>
        /// 根据句柄获取数据项
        /// </summary>
        public OpcItem? GetItem(uint handle)
        {
            return _items.TryGetValue(handle, out var item) ? item : null;
        }

        /// <summary>
        /// 根据 ItemID 获取数据项
        /// </summary>
        public OpcItem? GetItem(string itemId)
        {
            if (_itemIdToHandle.TryGetValue(itemId, out var handle))
            {
                return GetItem(handle);
            }
            return null;
        }

        /// <summary>
        /// 移除数据项
        /// </summary>
        public bool RemoveItem(uint handle)
        {
            if (_items.TryGetValue(handle, out var item))
            {
                _items.Remove(handle);
                _itemIdToHandle.Remove(item.ItemID);
                return true;
            }
            return false;
        }

        /// <summary>
        /// 获取所有数据项
        /// </summary>
        public IEnumerable<OpcItem> GetAllItems()
        {
            return _items.Values;
        }

        /// <summary>
        /// 检查 ItemID 是否存在
        /// </summary>
        public bool ItemExists(string itemId)
        {
            return _itemIdToHandle.ContainsKey(itemId);
        }

        /// <summary>
        /// 更新数据项的值
        /// </summary>
        public bool UpdateItemValue(string itemId, object newValue)
        {
            var item = GetItem(itemId);
            if (item != null)
            {
                item.UpdateValue(newValue);
                return true;
            }
            return false;
        }

        /// <summary>
        /// 获取数据项数量
        /// </summary>
        public int Count => _items.Count;
    }
}
