<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <OutputType>Exe</OutputType>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
    <EnableComHosting>true</EnableComHosting>
    <ComVisible>true</ComVisible>
    <RegisterForComInterop>true</RegisterForComInterop>
    <GenerateAssemblyInfo>true</GenerateAssemblyInfo>
    <AssemblyTitle>OPC DA Server</AssemblyTitle>
    <AssemblyDescription>Simple OPC DA Server Implementation</AssemblyDescription>
    <AssemblyVersion>*******</AssemblyVersion>
    <AssemblyFileVersion>*******</AssemblyFileVersion>
    <Guid>12345678-1234-1234-1234-123456789ABC</Guid>
    <PublishSingleFile>true</PublishSingleFile>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="System.Runtime.InteropServices" Version="4.3.0" />
  </ItemGroup>

</Project>
