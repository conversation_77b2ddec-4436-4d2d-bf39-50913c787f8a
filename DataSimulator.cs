using System.Timers;

namespace OpcDaServer
{
    /// <summary>
    /// 数据模拟器 - 生成和更新模拟数据
    /// </summary>
    public class DataSimulator : IDisposable
    {
        private readonly OpcItemManager _itemManager;
        private readonly System.Timers.Timer _updateTimer;
        private readonly Random _random = new();
        private bool _disposed = false;

        // 模拟数据的基础值
        private readonly Dictionary<string, SimulationConfig> _simulationConfigs = new();

        public DataSimulator(OpcItemManager itemManager)
        {
            _itemManager = itemManager;
            
            // 创建定时器，每秒更新一次数据
            _updateTimer = new System.Timers.Timer(1000);
            _updateTimer.Elapsed += OnTimerElapsed;
            _updateTimer.AutoReset = true;

            InitializeSimulationData();
        }

        /// <summary>
        /// 初始化 10 个模拟数据点
        /// </summary>
        private void InitializeSimulationData()
        {
            // Tag001-Tag004: 整数类型，递增模式
            for (int i = 1; i <= 4; i++)
            {
                string itemId = $"Tag{i:D3}";
                _itemManager.AddItem(itemId, typeof(int), i * 10);
                _simulationConfigs[itemId] = new SimulationConfig
                {
                    SimulationType = SimulationType.Incremental,
                    BaseValue = i * 10,
                    CurrentValue = i * 10,
                    Increment = i,
                    MinValue = 0,
                    MaxValue = 1000
                };
            }

            // Tag005-Tag007: 浮点类型，正弦波模式
            for (int i = 5; i <= 7; i++)
            {
                string itemId = $"Tag{i:D3}";
                _itemManager.AddItem(itemId, typeof(float), 0.0f);
                _simulationConfigs[itemId] = new SimulationConfig
                {
                    SimulationType = SimulationType.Sine,
                    BaseValue = 50.0,
                    Amplitude = 30.0,
                    Frequency = 0.1 * (i - 4), // 不同频率
                    Phase = 0
                };
            }

            // Tag008-Tag009: 布尔类型，随机切换
            for (int i = 8; i <= 9; i++)
            {
                string itemId = $"Tag{i:D3}";
                _itemManager.AddItem(itemId, typeof(bool), false);
                _simulationConfigs[itemId] = new SimulationConfig
                {
                    SimulationType = SimulationType.Random,
                    CurrentValue = false
                };
            }

            // Tag010: 字符串类型，状态切换
            _itemManager.AddItem("Tag010", typeof(string), "RUNNING");
            _simulationConfigs["Tag010"] = new SimulationConfig
            {
                SimulationType = SimulationType.Status,
                CurrentValue = "RUNNING",
                StatusValues = new[] { "RUNNING", "STOPPED", "PAUSED", "ERROR" },
                StatusIndex = 0
            };

            Console.WriteLine("已初始化 10 个模拟数据点:");
            Console.WriteLine("Tag001-Tag004: 整数类型 (递增模式)");
            Console.WriteLine("Tag005-Tag007: 浮点类型 (正弦波模式)");
            Console.WriteLine("Tag008-Tag009: 布尔类型 (随机模式)");
            Console.WriteLine("Tag010: 字符串类型 (状态切换模式)");
        }

        /// <summary>
        /// 启动数据模拟
        /// </summary>
        public void Start()
        {
            _updateTimer.Start();
            Console.WriteLine("数据模拟器已启动");
        }

        /// <summary>
        /// 停止数据模拟
        /// </summary>
        public void Stop()
        {
            _updateTimer.Stop();
            Console.WriteLine("数据模拟器已停止");
        }

        /// <summary>
        /// 定时器事件处理
        /// </summary>
        private void OnTimerElapsed(object? sender, ElapsedEventArgs e)
        {
            UpdateSimulationData();
        }

        /// <summary>
        /// 更新模拟数据
        /// </summary>
        private void UpdateSimulationData()
        {
            foreach (var kvp in _simulationConfigs)
            {
                string itemId = kvp.Key;
                var config = kvp.Value;
                object newValue = GenerateValue(config);
                
                _itemManager.UpdateItemValue(itemId, newValue);
            }
        }

        /// <summary>
        /// 根据配置生成新的值
        /// </summary>
        private object GenerateValue(SimulationConfig config)
        {
            switch (config.SimulationType)
            {
                case SimulationType.Incremental:
                    config.CurrentValue = (double)config.CurrentValue + config.Increment;
                    if ((double)config.CurrentValue > config.MaxValue)
                        config.CurrentValue = config.MinValue;
                    return (int)(double)config.CurrentValue;

                case SimulationType.Sine:
                    config.Phase += config.Frequency * 2 * Math.PI;
                    double sineValue = config.BaseValue + config.Amplitude * Math.Sin(config.Phase);
                    return (float)sineValue;

                case SimulationType.Random:
                    return _random.NextDouble() > 0.5;

                case SimulationType.Status:
                    if (_random.NextDouble() > 0.9) // 10% 概率切换状态
                    {
                        config.StatusIndex = (config.StatusIndex + 1) % config.StatusValues!.Length;
                        config.CurrentValue = config.StatusValues[config.StatusIndex];
                    }
                    return config.CurrentValue;

                default:
                    return config.CurrentValue;
            }
        }

        public void Dispose()
        {
            if (!_disposed)
            {
                _updateTimer?.Dispose();
                _disposed = true;
            }
        }
    }

    /// <summary>
    /// 模拟配置类
    /// </summary>
    public class SimulationConfig
    {
        public SimulationType SimulationType { get; set; }
        public double BaseValue { get; set; }
        public object CurrentValue { get; set; } = 0;
        public double Increment { get; set; } = 1;
        public double MinValue { get; set; } = 0;
        public double MaxValue { get; set; } = 100;
        public double Amplitude { get; set; } = 1;
        public double Frequency { get; set; } = 1;
        public double Phase { get; set; } = 0;
        public string[]? StatusValues { get; set; }
        public int StatusIndex { get; set; } = 0;
    }

    /// <summary>
    /// 模拟类型枚举
    /// </summary>
    public enum SimulationType
    {
        Incremental,    // 递增
        Sine,          // 正弦波
        Random,        // 随机
        Status         // 状态切换
    }
}
